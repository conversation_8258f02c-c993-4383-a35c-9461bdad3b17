version: "3.8"

services:
    app:
        build:
            context: ./php
        image: station-one-app:with-xdebug
        user: '1000'
        working_dir: /var/www/symfony4
        container_name: station-one-app
        env_file:
            - .env
        environment:
            # Xdebug v3 defaults (can be overridden via .env or docker-compose.override.yml)
            XDEBUG_MODE: "debug,develop"
            XDEBUG_START_WITH_REQUEST: "yes"
            XDEBUG_CLIENT_HOST: "host.docker.internal"
            XDEBUG_CLIENT_PORT: "9003"
            XDEBUG_IDEKEY: "PHPSTORM"
            # Helps PhpStorm map the server by name
            PHP_IDE_CONFIG: "serverName=station-one"
            # Optional: set to 7 to debug Xdebug connection issues
            XDEBUG_LOG_LEVEL: "0"
        extra_hosts:
            # Make host.docker.internal work on Linux
            - "host.docker.internal:host-gateway"
        ports:
            # Xdebug 3 default port
            - "9003:9003"
        volumes:
            - ./symfony4:/var/www/symfony4
            - ./php/app.ini:/usr/local/etc/php/conf.d/local.ini
        depends_on:
            - db
            - redis
            - yarn-encore
            - blackfire
            - mailcatcher
        networks:
            - default

    nginx:
        image: nginx
        restart: on-failure
        ports:
            - "80:80"
            - "443:443"
        volumes:
            - ./symfony4:/var/www/symfony4
            - ./nginx/templates:/etc/nginx/templates
            - ./nginx/certs:/etc/nginx/certs
        networks:
            - default
        depends_on:
            - app

    db:
        image: mysql:5.6.49
        command: --default-authentication-plugin=mysql_native_password
        ports:
            - "3306:3306"
        env_file:
            - .env
        volumes:
            - database:/var/lib/mysql
        networks:
            - default

    redis:
        image: redis
        networks:
            - default

    yarn-encore:
        image: glregistry.boost.open.global/izberg/station_one/docker/yarn:latest
        volumes:
            - ./symfony4:/app

    mailcatcher:
        image: glregistry.boost.open.global/izberg/station_one/docker/mailcatcher:latest
        ports:
            - '1080:1080'
        networks:
            - default
volumes:
    database:
    database_test:
