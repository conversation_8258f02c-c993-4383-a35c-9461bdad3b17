<?php

namespace Open\FrontVendorBundle\Controller;

use A<PERSON><PERSON><PERSON><PERSON>\Controller\MkoController;
use KnpU\OAuth2ClientBundle\Client\ClientRegistry;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class SecurityController extends MkoController
{

    #[\Symfony\Component\Routing\Attribute\Route(path: '/front/vendor/connect/', name: 'front_vendor_connect_check', options: ['i18n' => false])]
    public function connectCheckAction(Request $request)
    {
        die($request->getSession()->get('izberg_vendor_client'));
        return $this->redirectToRoute('vendor_homepage');
    }

    /**
     * @param ClientRegistry $clientRegistry
     * @return \Symfony\Component\HttpFoundation\RedirectResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/front/vendor/connect/start/', name: 'front_vendor_connect_start', options: ['i18n' => false])]
    public function connectAction(ClientRegistry $clientRegistry)
    {
        dump($clientRegistry
            ->getClient('izberg_vendor_client'));
        die();
        return $clientRegistry
            ->getClient('izberg_vendor_client')
            ->redirect([], ['approval_prompt' => 'none', 'scope' => 'openid profile merchant:read']);
    }

    #[\Symfony\Component\Routing\Attribute\Route(path: '/front/vendor/logout', name: 'vendor.logout', options: ['i18n' => false])]
    public function logoutAction()
    {
        // This is used to logout the symfony user from his session
    }
}
