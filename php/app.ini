; Local PHP configuration for Station One
; This file is mounted into the container at /usr/local/etc/php/conf.d/local.ini

; =========
; Xdebug v3
; =========
; Ensure the xdebug extension is enabled. The custom PHP image should include it.
; If the extension is not available, consult the image/Dockerfile to install it (pecl install xdebug).
zend_extension=xdebug

; Core Xdebug settings
[xdebug]
; Enable debugger and developer helpers (stack traces, etc.)
xdebug.mode=${XDEBUG_MODE}
; Start a debugging session for every request unless explicitly disabled
xdebug.start_with_request=${XDEBUG_START_WITH_REQUEST}
; Where PhpStorm is running. We resolve to host.docker.internal which we map in docker-compose
xdebug.client_host=${XDEBUG_CLIENT_HOST}
; Xdebug v3 default port
xdebug.client_port=${XDEBUG_CLIENT_PORT}
; Set to 0 because with nginx->php-fpm in Docker the discovered IP would be the nginx container, not the host
xdebug.discover_client_host=0
; PhpStorm default IDE key
xdebug.idekey=${XDEBUG_IDEKEY}
; Optional logging (set XDEBUG_LOG_LEVEL=7 to debug issues)
xdebug.log_level=${XDEBUG_LOG_LEVEL}
; xdebug.log=/tmp/xdebug.log

; Quality of life
xdebug.max_nesting_level=1024
