# Station One PHP runtime with Xdebug enabled
# We extend the existing base image and ensure Xdebug v3 is installed and enabled

FROM glregistry.boost.open.global/izberg/cubotoo/cubotoo/php:8.1-latest

# Install Xdebug via PECL if not already present and enable it
# docker-php-ext-enable will gracefully enable if the extension exists
RUN set -eux \
    && (php -m | grep -qi xdebug || (pecl install xdebug && docker-php-ext-enable xdebug)) \
    && php -v && php -m | sort

# The actual Xdebug runtime configuration is provided by a mounted file:
#   ./php/app.ini -> /usr/local/etc/php/conf.d/local.ini
# See php/app.ini for the settings driven by environment variables.
