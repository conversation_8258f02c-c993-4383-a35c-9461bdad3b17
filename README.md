# Station One

---
### _Official project documentation : [here](https://open.pages.boost.open.global/izberg/station_one/station-one/)_
---

## PHP Xdebug in Docker

This repository is configured to enable Xdebug v3 inside the `app` container and to work with PhpStorm for step-by-step debugging.

### What was added
- `php/app.ini` is mounted as `/usr/local/etc/php/conf.d/local.ini` and contains the Xdebug v3 configuration.
- `docker-compose.yml` sets sensible defaults for Xdebug and maps `host.docker.internal` to the host on Linux, and exposes port `9003`.

### Environment defaults (override if needed)
These are set in `docker-compose.yml` under the `app` service and can be overridden in your `.env` or a `docker-compose.override.yml`:
- `XDEBUG_MODE=debug,develop`
- `XDEBUG_START_WITH_REQUEST=yes`
- `XDEBUG_CLIENT_HOST=host.docker.internal`
- `XDEBUG_CLIENT_PORT=9003`
- `XDEBUG_IDEKEY=PHPSTORM`
- `PHP_IDE_CONFIG=serverName=station-one`

### Starting containers
```
docker compose up -d --build
```
The app image is now built from ./php/Dockerfile, which ensures Xdebug v3 is installed and enabled. The runtime settings are provided by `php/app.ini` and environment variables in `docker-compose.yml`.

### Using Makefile shortcuts
You can also use the project's Makefile targets which wrap docker compose commands for the development stack in `.dev-env/docker-compose`:
- Build images: `make build`
- Start stack: `make start`
- Tail logs: `make logs`
- Stop and remove: `make stop` (or `make clean`)
- Install Symfony app (composer, DB, assets): `make install`

Run `make help` to see all available targets.

### PhpStorm configuration
1. Open Preferences > PHP > Debug.
   - Debug port: 9003 (Xdebug).
   - Enable “Can accept external connections” (the phone icon in the toolbar should be green / listening).
2. Set up a Server (Preferences > PHP > Servers):
   - Name: `station-one` (must match `serverName=station-one`).
   - Host: `localhost` (or the domain you use in the browser).
   - Port: 80 (or 443 if using HTTPS) and check “Use path mappings”.
   - Map local path to container path:
     - Local: `<your local absolute path>/symfony4`
     - Remote: `/var/www/symfony4`
3. If you use Docker-based PHP Interpreter in PhpStorm (optional), ensure it points to the `app` container and has the same path mapping.

### Verifying Xdebug connection
- In PhpStorm, click the “Start Listening for PHP Debug Connections” button.
- Place a breakpoint in your Symfony controller (e.g., `symfony4/src/...Controller.php`).
- Open the application in your browser (through nginx on http://localhost/). The request should stop on your breakpoint.
- If it does not connect:
  - Confirm the container can resolve the host: `host.docker.internal` is mapped via `extra_hosts`.
  - Ensure your OS firewall allows inbound connections on port 9003.
  - Set `XDEBUG_LOG_LEVEL=7` in `docker-compose.yml` and restart, then inspect `/tmp/xdebug.log` inside the `app` container.

### Notes
- Port mapping `9003:9003` is included for clarity. Xdebug initiates the connection from container to host; the mapping is not strictly required but harmless.
- If your host cannot resolve `host.docker.internal`, the compose file already maps it to the Docker host gateway on Linux.

### Using a reverse proxy (nginx/traefik, HTTPS offloading)
If you access the app through a reverse proxy (different nginx in front, HTTPS offloading, custom domain), make sure:
- Symfony trusts your proxy and host headers. In dev we enable this by default in `symfony4/.env`:
  - `TRUSTED_PROXIES` includes private networks (*********/8, 10.0.0.0/8, **********/12, ***********/16).
  - `TRUSTED_HOSTS` allows localhost, 127.0.0.1, alstom.local and vendor.station-one.com.
- Your proxy forwards these headers to nginx/PHP-FPM:
  - `X-Forwarded-For`, `X-Forwarded-Proto`, and `X-Forwarded-Host`.
This prevents issues such as invalid OAuth state, wrong scheme (http vs https), and cookies not being sent.

Xdebug: the debugger connects out from the PHP container to your IDE. If your IDE runs on a different machine than the Docker host, set `XDEBUG_CLIENT_HOST` accordingly (e.g., your workstation IP) in `docker-compose.override.yml` or `.env`.
