#docker-compose vars
DOCKER_COMPOSE_DIR := .dev-env/docker-compose

DOCKER_COMPOSE_FILES = -f $(DOCKER_COMPOSE_DIR)/base.yml
DOCKER_COMPOSE_FILES += -f $(DOCKER_COMPOSE_DIR)/symfony4.yml
DOCKER_COMPOSE_FILES += -f $(DOCKER_COMPOSE_DIR)/reactapplication.yml

DOCKER_COMPOSE_ENV_FILE := $(DOCKER_COMPOSE_DIR)/.env

# check for .env.local file
ifeq ($(shell test -e $(DOCKER_COMPOSE_DIR)/.env.local && echo -n yes),yes)
    DOCKER_COMPOSE_ENV_FILE := $(DOCKER_COMPOSE_DIR)/.env.local
endif

docker_version != docker compose --version | cut -d " " -f 3 | sed "s/\.//g" | sed "s/,//g"
docker_compose := docker compose --env-file $(DOCKER_COMPOSE_ENV_FILE) $(DOCKER_COMPOSE_FILES) -p $(PROJECT_NAME)

## Start dev env
start: start-requirements
	$(docker_compose) up -d

## Remove dev env
stop:
	$(docker_compose) down --remove-orphans

## Remove dev env
clean: stop

install: symfony4.install

start-requirements:
	@if [ $(docker_version) -lt 1290 ] ; then \
    	echo "docker-compose version should be > 1.29.0"; \
    	exit 1; \
	fi
